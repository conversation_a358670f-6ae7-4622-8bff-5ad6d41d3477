import {
  Is<PERSON>rray,
  IsS<PERSON>,
  IsNumber,
  ValidateNested,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';

class From {
  @IsString()
  @IsOptional()
  id: string;

  @IsString()
  @IsOptional()
  username: string;
}

class Media {
  @IsString()
  @IsOptional()
  id: string;

  @IsString()
  @IsOptional()
  media_product_type: string;
}

class Value {
  @ValidateNested()
  @Type(() => From)
  @IsOptional()
  from: From;

  @ValidateNested()
  @Type(() => Media)
  @IsOptional()
  media: Media;

  @IsString()
  @IsOptional()
  id: string;

  @IsString()
  @IsOptional()
  parent_id: string;

  @IsString()
  @IsOptional()
  text: string;
}

export class Change {
  @ValidateNested()
  @Type(() => Value)
  @IsOptional()
  value: Value;

  @IsString()
  @IsOptional()
  field: string;
}

export class Entry {
  @IsString()
  @IsOptional()
  id: string;

  @IsNumber()
  @IsOptional()
  time: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Change)
  @IsOptional()
  changes: Change[];
}

export class WebhookDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Entry)
  entry: Entry[];

  @IsString()
  @IsOptional()
  object: string;
}
