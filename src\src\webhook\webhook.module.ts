import { Modu<PERSON> } from '@nestjs/common';
import { WebhookController } from './webhook.controller';
import { WebhookService } from './webhook.service';
import { DatabaseModule } from '../database/database.module';
import { AiModule } from '../ai/ai.module';
import { WebhookExtractorService } from './services/webhook-extractor.service';
import { MetaModule } from '../meta/meta.module';

@Module({
  imports: [DatabaseModule, AiModule, MetaModule],
  controllers: [WebhookController],
  providers: [WebhookService, WebhookExtractorService],
})
export class WebhookModule {}
