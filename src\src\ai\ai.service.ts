import { Injectable } from '@nestjs/common';
import { AiSetting } from '../database/entities/ai-settings.entity';
import { IAiProvider } from '../interfaces/ai-provider.interface';
import { PromptBuilderService } from './prompts/prompt-builder.service';
import { TokenManagerService } from './services/token-manager.service';
import { AiProviderFactory } from './services/ai-provider-factory.service';

/**
 * AiService
 *
 * The `AiService` is responsible for interacting with AI providers to generate responses
 * based on user comments nder Instagram posts.
 * It leverages multiple services:
 * - `PromptBuilderService`: Builds a prompt based on AI settings.
 * - `TokenManagerService`: Manages and retrieves API tokens for authentication.
 * - `AiProviderFactory`: Selects and provides the appropriate AI provider based
 * on the configured `ai_driver`.
 *
 * The service uses a dynamic provider selection pattern, where the AI provider
 * is selected based on the `ai_driver` defined in `AiSetting`.
 * Once the correct provider is selected, it generates a reply using
 * the provided prompt and user comment.
 */
@Injectable()
export class AiService {
  constructor(
    private readonly promptBuilderService: PromptBuilderService,
    private readonly tokenManager: TokenManagerService,
    private readonly aiProviderFactory: AiProviderFactory,
  ) {}

  /**
   * Generates an AI-generated reply based on user comment, AI settings, and the admin's token.
   *
   * This method builds a system prompt from the provided `aiSetting` and generates a reply
   * by passing the prompt, user comment, and the admin's API token to the selected AI provider.
   * It first checks if the AI provider exists using the `ai_driver` from `aiSetting`, then it
   * uses that provider to generate and return a reply.
   *
   * @param {AiSetting} aiSettings - The settings specific to the AI configuration for the post
   * @param {string} userComment - The comment left by the user under the Instagram post
   * @param {string} adminId - The ID of the admin for whom the AI reply is being generated
   * @returns {Promise<string>} - A promise that resolves to the generated AI reply based on the user comment
   * @throws {Error} - Throws an error if the selected AI provider is not available or the token cannot be fetched
   */
  public async getAiReply(
    aiSettings: AiSetting,
    userComment: string,
    adminId: string,
  ): Promise<string> {
    const prompt: string = this.promptBuilderService.buildPrompt({
      post_description: aiSettings.post_description,
      friendly_tone: aiSettings.friendly_tone,
      bot_character: aiSettings.bot_character,
      custom_prompt: aiSettings.custom_prompt,
    });

    const adminAiToken: string = await this.tokenManager.getToken(adminId);

    const aiProvider: IAiProvider = this.aiProviderFactory.getProvider(
      aiSettings.ai_driver,
    );

    return await aiProvider.generateReply(prompt, userComment, adminAiToken);
  }
}
