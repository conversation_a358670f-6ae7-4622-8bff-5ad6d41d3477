import { Module } from '@nestjs/common';
import { AiService } from './ai.service';
import { PromptBuilderService } from './prompts/prompt-builder.service';
import { AiProviderFactory } from './services/ai-provider-factory.service';
import { OpenAiProvider } from './providers/openai.provider';
import { TokenManagerService } from './services/token-manager.service';
import { DirectamApiModule } from '../directam-api/directam-api.module';

@Module({
  imports: [DirectamApiModule],
  providers: [
    AiService,
    AiProviderFactory,
    PromptBuilderService,
    TokenManagerService,
    OpenAiProvider,
  ],
  exports: [
    AiService,
    AiProviderFactory,
    PromptBuilderService,
    TokenManagerService,
  ],
})
export class AiModule {}
