import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { WebhookExtractedData } from '../webhook/services/webhook-extractor.service';
import { IMetaApiHandler } from '../interfaces/meta-api-handler.interface';
import { MetaApiHandlerFactoryService } from './services/meta-api-handler-factory.service';

@Injectable()
export class MetaService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly metaFactory: MetaApiHandlerFactoryService,
  ) {}

  /**
   * Reply to a comment on the correct platform (Instagram or Facebook).
   * @param {WebhookExtractedData} webhookData - Extracted data from the webhook.
   * @param {string} replyMessage - The message to be sent as a reply.
   * @returns {Promise<MetaResponse>} - The response from the platform API.
   */
  public async replyToComment(
    webhookData: WebhookExtractedData,
    replyMessage: string,
  ): Promise<MetaResponse> {
    const metaData = await this.databaseService.getMetaDataForAdmin(
      webhookData.adminId,
    );
    if (!metaData) {
      throw new Error('No Meta data found for the user');
    }

    const platformService: IMetaApiHandler =
      this.metaFactory.getPlatformService(metaData.platform);

    return platformService.replyCommentWithComment(
      webhookData.commentId,
      replyMessage,
      metaData.accessToken,
    );
  }
}
