/**
 * Interface IMetaApiHandler
 *
 * This interface defines the contract for handling Meta API requests (e.g., Instagram and Facebook).
 * It provides a method to send a reply to a comment using the appropriate Meta platform API.
 */
export interface IMetaApiHandler {
  /**
   * Sends a reply to a comment on a Meta platform (Instagram or Facebook).
   *
   * This method is responsible for sending the reply to the specified comment using the
   * appropriate platform's API (Instagram or Facebook) and returning the response from the API.
   *
   * It is typically used for:
   * - **Replying to comments** on posts under Instagram or Facebook
   * - **Interacting with users** via direct messages or comments on Meta platforms
   *
   * The Meta platform API must:
   * - Correctly identify the comment being replied to
   * - Send the provided message as a reply to the comment
   * - Authenticate the request using the access token provided
   *
   * @param commentId The ID of the comment to reply to.
   * @param message The message that should be sent as the reply.
   * @param accessToken The access token for the admin user to authenticate the API call.
   * @returns A `Promise` resolving to the response from the Meta API, which includes the status of the operation (success/failure, etc.).
   *
   * @example **Replying to a comment on Instagram**
   * ```ts
   * const commentId = '17881918029214882';
   * const message = 'Thanks for your comment!';
   * const accessToken = 'IGQWROcThtVWZAhNkh5cTVKWlRNZAXltdWFPel82a25XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX';
   *
   * const response = await metaApiHandler.replyCommentWithComment(commentId, message, accessToken);
   * console.log(response); // e.g., { id: '18185204464314445' }
   * ```
   */
  replyCommentWithComment(
    commentId: string,
    message: string,
    accessToken: string,
  ): Promise<MetaResponse>;
}
