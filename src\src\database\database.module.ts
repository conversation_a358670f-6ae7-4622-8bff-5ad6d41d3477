import { Module } from '@nestjs/common';
import { DatabaseService } from './database.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AiSetting } from './entities/ai-settings.entity';
import { Page } from './entities/pages.entity';
import { EncryptionService } from './services/encryption.service';

@Module({
  imports: [TypeOrmModule.forFeature([Page, AiSetting])],
  providers: [DatabaseService, EncryptionService],
  exports: [DatabaseService],
})
export class DatabaseModule {}
