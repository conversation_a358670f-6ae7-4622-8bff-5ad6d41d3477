import { Injectable } from '@nestjs/common';
import { Change, Entry, WebhookDto } from '../dto/webhook.dto';

/**
 * WebhookExtractedData
 *
 * This interface represents the structure of the data that is extracted from an incoming webhook.
 * Properties:
 * - `adminId` (string): The unique identifier of the admin responsible for the post.
 * - `userId` (string): The unique identifier of the user who made the comment under admin post.
 * - `postId` (string): The unique identifier of the post under which the comment was made.
 * - `commentId` (string): The unique identifier of the comment made by the user.
 * - `userComment` (string): The actual text of the comment left by the user.
 */
export interface WebhookExtractedData {
  adminId: string;
  userId: string;
  postId: string;
  commentId: string;
  userComment: string;
}

/**
 * WebhookExtractorService
 *
 * The `WebhookExtractorService` is responsible for extracting key information from
 * raw webhook data.
 * It parses the `WebhookDto` and returns an object containing essential fields
 * such as admin ID, user ID, post-ID, comment ID, and the actual user comment.
 *
 * This service is used by the `WebhookService` to convert raw data into a more
 * usable format.
 */
@Injectable()
export class WebhookExtractorService {
  /**
   * Extracts relevant data (adminId, userId, postId, commentId, userComment)
   * from the provided webhook data.
   *
   * @param {WebhookDto} webhookData - The incoming webhook data that needs to be processed
   * @returns {WebhookExtractedData} - The extracted fields: adminId, userId,
   * postId, commentId, and userComment
   */
  public extract(webhookData: WebhookDto): WebhookExtractedData {
    const entry: Entry = webhookData.entry[0];
    const changes: Change[] = entry.changes || [];

    const adminId: string = entry.id;
    const userId: string = changes[0]?.value?.from?.id;
    const postId: string = changes[0]?.value?.media?.id;
    const commentId: string = changes[0]?.value?.id;
    const userComment: string = changes[0]?.value?.text;

    return {
      adminId,
      userId,
      postId,
      commentId,
      userComment,
    };
  }
}
