import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  DeleteDateColumn,
} from 'typeorm';

@Entity('ai_settings')
export class AiSetting {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'bigint' })
  user_id: number;

  @Column({ type: 'varchar', length: 255 })
  post_id: string;

  @Column({ type: 'int', nullable: true })
  friendly_tone: number;

  @Column({ type: 'text', nullable: true })
  post_description: string;

  @Column({ type: 'text', nullable: true })
  bot_character: string;

  @Column({ type: 'text', nullable: true })
  custom_prompt: string;

  @Column({ type: 'varchar', length: 255 })
  ai_driver: string;

  @Column({ type: 'int', default: 0 })
  remaining_comments: number;

  @Column({ type: 'int', default: 1 })
  remaining_edit_posts: number;

  @Column({ type: 'timestamp', nullable: true })
  created_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}
