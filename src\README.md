# Instagram AI Bot Service

A NestJS-based microservice that automatically generates and posts AI-powered replies to Instagram comments using webhook integration.

## Architecture Overview

This service processes Instagram webhooks, generates contextual AI replies using OpenAI, and posts responses back to Instagram/Facebook via Meta's Graph API.

## Folder Structure

```
src/
├── ai/                     # AI processing and provider management
├── app.*                   # Main application entry point
├── database/               # Database entities and services
├── directam-api/           # External API integration
├── interfaces/             # TypeScript interfaces
├── meta/                   # Meta (Instagram/Facebook) API integration
└── webhook/                # Webhook handling and processing
```

## Module Descriptions

### 🤖 AI Module (`src/ai/`)
Handles AI-powered reply generation with multiple provider support.

**Key Components:**
- `AiService` - Main service for generating AI replies
- `AiProviderFactory` - Factory pattern for AI provider selection
- `OpenAiProvider` - OpenAI API integration
- `PromptBuilderService` - Builds contextual prompts from settings
- `TokenManagerService` - Manages and caches API tokens

**Responsibilities:**
- Generate contextual replies based on user comments
- Manage AI provider selection (currently OpenAI)
- Build system prompts from admin settings
- Handle API token management and caching

### 📊 Database Module (`src/database/`)
Manages data persistence and encryption services.

**Key Components:**
- `DatabaseService` - Main database operations
- `EncryptionService` - Token encryption/decryption
- `AiSetting` entity - AI configuration per post
- `Page` entity - Admin page metadata

**Responsibilities:**
- Store AI settings and configurations
- Manage admin page access tokens (encrypted)
- Handle comment credit tracking
- Provide metadata for API calls

### 🔗 Webhook Module (`src/webhook/`)
Processes incoming Instagram webhooks and orchestrates the reply flow.

**Key Components:**
- `WebhookController` - HTTP endpoint for webhook reception
- `WebhookService` - Webhook processing logic
- `WebhookExtractorService` - Extracts data from webhook payload
- `WebhookDto` - Validation for incoming webhook data

**Responsibilities:**
- Receive Instagram comment webhooks
- Extract comment and user data
- Trigger background AI reply generation
- Coordinate with AI and Meta services

### 📱 Meta Module (`src/meta/`)
Handles communication with Instagram and Facebook APIs.

**Key Components:**
- `MetaService` - Main Meta platform service
- `InstagramApiService` - Instagram-specific API calls
- `FacebookApiService` - Facebook-specific API calls
- `MetaApiHandlerFactory` - Platform selection factory
- `MetaApiClientService` - HTTP client for Meta APIs

**Responsibilities:**
- Send replies to Instagram/Facebook comments
- Handle platform-specific API differences
- Manage access tokens and authentication
- Abstract Meta API complexity

### 🌐 DirectAM API Module (`src/directam-api/`)
Integrates with external DirectAM service for token management.

**Key Components:**
- `DirectamApiService` - External API client

**Responsibilities:**
- Fetch admin-specific OpenAI tokens
- Handle external service authentication
- Provide fallback token management

## Data Flow

1. **Webhook Reception**: Instagram sends comment webhook to `/webhook/instagram`
2. **Data Extraction**: Extract admin ID, user ID, post ID, comment ID, and comment text
3. **Settings Lookup**: Fetch AI settings for the specific post
4. **AI Generation**: Build prompt and generate reply using configured AI provider
5. **Reply Posting**: Send generated reply back to Instagram/Facebook
6. **Credit Management**: Decrement remaining comment credits

## Key Interfaces

- `IAiProvider` - Contract for AI service providers
- `IMetaApiHandler` - Contract for Meta platform handlers
- `WebhookExtractedData` - Structured webhook data

## Environment Variables

```env
# Database
DB_HOST=localhost
DB_PORT=5432
DB_USER=username
DB_PASSWORD=password
DB_NAME=database

# OpenAI
OPENAI_API_URL=https://api.openai.com/v1/chat/completions
OPENAI_MODEL=gpt-4

# DirectAM API
DIRECTAM_API_URL=https://api.directam.com
DIRECTAM_API_KEY=your_api_key

# Application
PORT=3000
```

## Getting Started

1. Install dependencies: `npm install`
2. Set up environment variables
3. Run database migrations
4. Start the service: `npm run start`

The service will listen for Instagram webhooks on `/webhook/instagram` and automatically process comments with AI-generated replies.