import { Injectable } from '@nestjs/common';
import { MetaApiClientService } from '../services/meta-api-client.service';

/**
 * Abstract class for Meta API communication.
 * This provides a common method for making API requests to Instagram or Facebook.
 */
@Injectable()
export abstract class MetaApiBaseService {
  protected baseUrl: string;
  protected apiVersion: string;

  protected constructor(
    protected readonly apiRequestService: MetaApiClientService,
  ) {
    this.setBaseUrl();
    this.setApiVersion();
  }

  /**
   * Abstract method to set the base URL for the platform.
   * This method will be implemented in the derived classes
   * (e.g., InstagramApiService, FacebookApiService).
   */
  protected abstract setBaseUrl(): void;

  /**
   * Abstract method to set the API version for the platform.
   * This method will be implemented in the derived classes
   * (e.g., InstagramApiService, FacebookApiService).
   */
  protected abstract setApiVersion(): void;

  /**
   * A common method for sending a reply to the comment, shared across Instagram and Facebook.
   * @param {string} commentId - The ID of the comment to reply to.
   * @param {string} message - The message to be sent as the reply.
   * @param {string} accessToken - The access token for the admin user.
   * @returns {Promise<MetaResponse>} - The response from the API.
   */
  public async replyCommentWithComment(
    commentId: string,
    message: string,
    accessToken: string,
  ): Promise<MetaResponse> {
    const apiUrl = `${this.baseUrl}/v${this.apiVersion}/${commentId}/replies`;

    const headers = {
      Authorization: `Bearer ${accessToken}`,
    };

    const params = {
      message: message,
    };

    return await this.apiRequestService.sendPostRequest(
      apiUrl,
      headers,
      params,
      null,
    );
  }
}
