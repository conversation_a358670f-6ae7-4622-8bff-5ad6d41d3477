import { Test, TestingModule } from '@nestjs/testing';
import { DirectamApiService } from './directam-api.service';

describe('DirectamApiService', () => {
  let service: DirectamApiService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [DirectamApiService],
    }).compile();

    service = module.get<DirectamApiService>(DirectamApiService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
