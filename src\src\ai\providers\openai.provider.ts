import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { IAiProvider } from '../../interfaces/ai-provider.interface';

@Injectable()
export class OpenAiProvider implements IAiProvider {
  public async generateReply(
    prompt: string,
    userComment: string,
    apiToken: string,
  ): Promise<string> {
    const payload = {
      model: process.env.OPENAI_MODEL,
      messages: [
        { role: 'system', content: prompt },
        { role: 'user', content: `This is my user comment: ${userComment}` },
      ],
      temperature: 0.8,
    };

    const response = await axios.post(process.env.OPENAI_API_URL!, payload, {
      headers: {
        Authorization: `Bearer ${apiToken}`,
        'Content-Type': 'application/json',
      },
      timeout: 20000,
    });

    return response.data.choices[0]?.message?.content || '';
  }
}
