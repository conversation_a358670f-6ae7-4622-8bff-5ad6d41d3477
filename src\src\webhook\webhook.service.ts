import { Injectable } from '@nestjs/common';
import {
  WebhookExtractedData,
  WebhookExtractorService,
} from './services/webhook-extractor.service';
import { WebhookDto } from './dto/webhook.dto';

@Injectable()
export class WebhookService {
  constructor(
    private readonly webhookExtractingService: WebhookExtractorService,
  ) {}

  /**
   * Processes the incoming webhook data and extracts the relevant fields.
   * This method delegates the task of extracting data to the `WebhookExtractorService`.
   *
   * @param {WebhookDto} webhookData - The incoming webhook data to be processed
   * @returns {WebhookExtractedData} - The extracted data including admin ID, user ID, post-ID,
   *         comment ID, and user comment
   */
  public processWebhook(webhookData: WebhookDto): WebhookExtractedData {
    return this.webhookExtractingService.extract(webhookData);
  }
}
