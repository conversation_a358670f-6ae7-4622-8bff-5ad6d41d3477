import { Injectable } from '@nestjs/common';
import { InstagramApiService } from './instagram-api.service';
import { FacebookApiService } from './facebook-api.service';
import { IMetaApiHandler } from '../../interfaces/meta-api-handler.interface';

/**
 * Factory service that provides the appropriate platform-specific Meta API handler
 * based on the platform name (Instagram or Facebook).
 */
@Injectable()
export class MetaApiHandlerFactoryService {
  constructor(
    private readonly instagramApiService: InstagramApiService,
    private readonly facebookApiService: FacebookApiService,
  ) {}

  /**
   * Gets the appropriate platform service (Instagram or Facebook).
   * @param {string} platform - The platform name (Instagram or Facebook).
   * @returns {IMetaApiHandler} - The appropriate platform service.
   */
  public getPlatformService(platform: string): IMetaApiHandler {
    switch (platform) {
      case 'instagram':
        return this.instagramApiService;
      case 'facebook':
        return this.facebookApiService;
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }
}
